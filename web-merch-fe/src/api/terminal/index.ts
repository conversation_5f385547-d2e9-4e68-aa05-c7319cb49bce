import type { CommonParams, CommonResult } from '@/api/common/types';
import { post } from '@/utils/request';

/**
 * @description 终端API
 */
export class TerminalApi {
  /**
   * 渠道商户终端报备列表
   */
  static queryChlMerchTerminalList = (data?: CommonParams) => post<CommonResult>({ url: '/app/terminal/queryChlMerchTerminalList', data });

  /**
   * 终端绑定
   */
  static tryBindTermSn = (data: CommonParams) => post<CommonResult>({ url: '/app/terminal/tryBindTermSn', data });

  /**
   * 终端绑定
   */
  static fetchInfoByTerminalSn = (data: CommonParams) => post<CommonResult>({ url: '/app/terminal/fetchInfoByTerminalSn', data });

  /**
   * 终端换绑
   */
  static changeBindTermSn = (data: CommonParams) => post<CommonResult>({ url: '/app/terminal/changeBindTermSn', data });

  /**
   * 根据终端SN查询权益订单列表
   */
  static queryTerminalActivePayOrderListBySn = (data: CommonParams) => post<CommonResult>({ url: '/app/terminal/queryTerminalActivePayOrderListBySn', data });
}
