<template>
  <view class="h-full overflow-hidden bg-primary">
    <view class="h-full flex flex-col">
      <view class="mb-10px shrink-0">
        <wd-tabs v-model="currentInfoTypeTab" @change="onChangeInfoTypeTab">
          <wd-tab title="基础信息" />
          <wd-tab title="报备信息" />
          <wd-tab title="终端信息" />
          <wd-tab title="交易汇总" />
        </wd-tabs>
      </view>

      <view class="grow overflow-hidden">
        <scroll-view
          scroll-y :show-scrollbar="false" :scroll-top="scrollTop" :throttle="false"
          class="h-full"
        >
          <!-- ? 基础信息 -->
          <template v-if="currentInfoTypeTab === 0">
            <wd-cell-group border>
              <wd-cell title="商户名称" :value="baseInfo.merchantName" title-width="100px" />
              <wd-cell title="商户编号" :value="baseInfo.merchantNo" title-width="100px" />
              <wd-cell title="注册手机号" :value="baseInfo.registerPhoneNo" title-width="100px" />
              <wd-cell title="团队名称" :value="baseInfo.directTeamName" title-width="100px" />
            </wd-cell-group>
          </template>

          <!-- ? 报备信息 -->
          <template v-if="currentInfoTypeTab === 1">
            <!-- 列表 -->
            <view class="px-20rpx pb-20rpx">
              <view
                v-for="(item, key) in reportInfo" :key="key"
                class="mb-20rpx rounded-xl bg-white p-26rpx shadow"
              >
                <view class="flex items-center">
                  <view class="cell-group grow">
                    <view class="cell">
                      <text class="cell-label">
                        报备简称:
                      </text>
                      <view class="cell-value">
                        <text>{{ item.chlMerchantShortName || '--' }}</text>
                        <i
                          v-if="!!item.chlMerchantShortName"
                          class="i-mdi-content-copy ml-10rpx size-28rpx text-#b51e1e"
                          @click.stop="copyData(item.chlMerchantShortName)"
                        />
                      </view>
                    </view>
                    <view class="cell">
                      <text class="cell-label">
                        通道商户编号:
                      </text>
                      <view class="cell-value">
                        <text>{{ item.chlMerchantNo || '--' }}</text>
                        <i
                          v-if="!!item.chlMerchantNo"
                          class="i-mdi-content-copy ml-10rpx size-28rpx text-#b51e1e"
                          @click.stop="copyData(item.chlMerchantNo)"
                        />
                      </view>
                    </view>
                    <view class="cell">
                      <text class="cell-label">
                        入网时间:
                      </text>
                      <text class="cell-value">
                        {{ item.createTime || '--' }}
                      </text>
                    </view>
                    <view class="cell">
                      <text class="cell-label">
                        入网状态:
                      </text>
                      <text class="cell-value">
                        {{ reportRespCodesMap[item.reportRespCode] || '--' }}
                      </text>
                    </view>
                    <view v-if="item.reportRespCode === 3 && item.reportRespMsg" class="cell !items-start">
                      <text class="cell-label">
                        入网失败原因:
                      </text>
                      <text class="cell-value">
                        {{ item.reportRespMsg }}
                      </text>
                    </view>
                    <view v-if="[3, 5].includes(loginUser.orgType as number) && item.reportRespCode === 2" class="cell">
                      <text class="cell-label">
                        结算政策:
                      </text>
                      <view class="cell-value">
                        <wd-tag custom-class="custom-tag-class" mark>
                          {{ item.policyNameDesc || '--' }}
                        </wd-tag>
                      </view>
                    </view>
                  </view>

                  <view v-if="[3, 5].includes(loginUser.orgType as number) && item.reportRespCode === 2" class="mt-20rpx">
                    <wd-button size="small" @click="toModifyRate(item)">
                      修改费率
                    </wd-button>
                  </view>
                </view>

                <!-- 渠道开通状态 -->
                <view
                  v-if="item.reportRespCode === 2"
                  class="mt-20rpx"
                >
                  <wd-tag custom-class="custom-tag-class" mark>
                    {{ formatTagDesc('union', item.unionPayOpenStatus) }}
                  </wd-tag>
                  <wd-tag custom-class="custom-tag-class" mark>
                    {{ formatTagDesc('wechat', item.wechatOpenStatus) }}
                  </wd-tag>
                  <wd-tag custom-class="custom-tag-class" mark>
                    {{ formatTagDesc('alipay', item.alipayOpenStatus) }}
                  </wd-tag>
                </view>
              </view>
            </view>
          </template>

          <!-- ? 终端信息 -->
          <template v-if="currentInfoTypeTab === 2">
            <view class="px-20rpx pb-20rpx">
              <view
                v-for="(item, key) in terminalInfo" :key="key"
                class="mb-20rpx border border-#edf0f3 rounded-lg border-solid bg-white"
                @click="toTerminalDetail(item.terminalSn)"
              >
                <view class="p-8px">
                  <view class="flex items-center">
                    <view class="grow">
                      <wd-cell-group custom-class="custom-cell-group">
                        <wd-cell title="设备SN">
                          <view class="flex items-center justify-end">
                            <text>{{ item.terminalSn }}</text>
                            <i v-if="item.terminalSn" class="copy-icon" @click="copyData(item.terminalSn)" />
                          </view>
                        </wd-cell>
                        <wd-cell title="绑定通道商户号">
                          <view class="flex items-center justify-end">
                            <text>{{ item.chlMerchantNo }}</text>
                            <i v-if="item.chlMerchantNo" class="copy-icon" @click="copyData(item.chlMerchantNo)" />
                          </view>
                        </wd-cell>
                        <wd-cell title="绑定时间" :value="item.bindTime" />
                        <wd-cell title="绑定状态" :value="unionSnBindStatusMap[item.bindStatus] || '--'" />
                        <wd-cell v-if="item.bindStatus === 2" title="绑定失败原因" :value="item.bindReasonMsg || '--'" />
                        <wd-cell title="银联终端报备状态" :value="unionSnReportStatusMap[item.unionReportStatus] || '--'" />
                        <wd-cell v-if="item.unionReportStatus === 2 && item.unionReportReason" title="银联失败原因" :value="item.unionReportReason" />
                      </wd-cell-group>
                    </view>

                    <view class="ml-5px shrink-0">
                      <wd-icon name="arrow-right" size="40rpx" />
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </template>

          <!-- ? 交易汇总 -->
          <template v-if="currentInfoTypeTab === 3">
            <view class="px-20rpx">
              <view class="flex flex-col items-center rounded-2 bg-blue p-20rpx text-center text-white">
                <view class="flex flex-col">
                  <text>累计交易</text>
                  <text>{{ totalTransInfo.totalTransAmt }}</text>
                </view>

                <view class="mt-10px w-full flex items-center">
                  <view class="w-50% flex flex-col">
                    <text>本月交易</text>
                    <text>{{ totalTransInfo.thisMonthTotalTransAmt }}</text>
                  </view>
                  <view class="w-50% flex flex-col">
                    <text>上月交易</text>
                    <text>{{ totalTransInfo.lastMonthTotalTransAmt }}</text>
                  </view>
                </view>
              </view>

              <view class="mt-10px flex p-20rpx">
                <custom-datetime-picker
                  v-model="whereDate"
                  type="date"
                  :default-value="whereDate"
                  @confirm="handleConfirmDate"
                />
              </view>
              <view class="rounded-2 bg-white py-20rpx text-center">
                <view class="flex flex-col">
                  <text>交易金额</text>
                  <text>{{ monthTransInfo.totalTransAmt }}</text>
                </view>

                <view class="mt-10px w-full flex items-center border-x-0 border-y border-slate-200 border-solid py-10px">
                  <view class="w-50% flex flex-col">
                    <text>借记卡交易</text>
                    <text>{{ monthTransInfo.debitCardTotalTransAmt }}</text>
                  </view>
                  <view class="w-50% flex flex-col">
                    <text>信用卡交易</text>
                    <text>{{ monthTransInfo.creditCardTotalTransAmt }}</text>
                  </view>
                </view>

                <view class="mt-10px w-full flex items-center">
                  <view class="w-33.3% flex flex-col">
                    <text>微信扫码</text>
                    <text>{{ monthTransInfo.wechatTotalTransAmt }}</text>
                  </view>
                  <view class="w-33.3% flex flex-col">
                    <text>支付宝扫码</text>
                    <text>{{ monthTransInfo.alipayTotalTransAmt }}</text>
                  </view>
                  <view class="w-33.3% flex flex-col">
                    <text>云闪付交易</text>
                    <text>{{ monthTransInfo.unionpayTotalTransAmt }}</text>
                  </view>
                </view>
              </view>
            </view>
          </template>
        </scroll-view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import NP from 'number-precision';
import { buildUrlWithParams, decodeUrlParams } from '@/utils';
import { MerchApi } from '@/api-org/merch';
import { useClipboard } from '@/hooks';
import { useUserStore } from '@/store';

type RouteParams = {
  merchantNo: string; // 商户编码,必填
};

const loginUser = computed(() => useUserStore().info);

// 路由参数
let routeParams: RouteParams;

const scrollTop = ref(0);

const currentInfoTypeTab = ref(0);

const baseInfo = ref<any>({});
const reportInfo = ref<any>([]);
const terminalInfo = ref<any>([]);
const totalTransInfo = ref<any>({});
const monthTransInfo = ref<any>({});

// 月交易汇总-查询时间范围
const whereStartDateDef = dayjs().startOf('month').valueOf();
const whereEndDateDef = dayjs().valueOf();
const whereDate = ref([whereStartDateDef, whereEndDateDef]);
const monthTransInfoWhere = reactive({
  selTimeType: 1,
  queryBeginTime: dayjs(whereStartDateDef).format('YYYY-MM-DD'), // 开始时间 yyyy-MM-dd
  queryEndTime: dayjs(whereEndDateDef).format('YYYY-MM-DD'), // 结束时间 yyyy-MM-dd
});

function handleConfirmDate({ value }: { value: string[] }) {
  [monthTransInfoWhere.queryBeginTime, monthTransInfoWhere.queryEndTime] = value;
  queryTotalTransInfByMutiCondition();
}

onLoad((query: any) => {
  query = decodeUrlParams(query);
  routeParams = Object.assign({}, query);
  onChangeInfoTypeTab({ index: currentInfoTypeTab.value });
});

function onChangeInfoTypeTab({ index }: { index?: number }) {
  scrollTop.value = 0;

  switch (index) {
    case 0:
      queryMerchantBaseInfo();
      break;
    case 1:
      queryMerchantReportInfoList();
      break;
    case 2:
      queryMerchantTerminalInfoList();
      break;
    case 3:
      queryTotalTransInfByMerchNo();
      queryTotalTransInfByMutiCondition();
      break;
  }
}

async function queryMerchantBaseInfo() {
  const data = await MerchApi.queryMerchantBaseInfo({
    merchantNo: routeParams.merchantNo,
  });
  baseInfo.value = Object.assign({}, data);
}

async function queryMerchantReportInfoList() {
  const data = await MerchApi.queryMerchantReportInfoList({
    merchantNo: routeParams.merchantNo,
  });
  reportInfo.value = Object.assign([], data);
}

async function queryMerchantTerminalInfoList() {
  const data = await MerchApi.queryMerchantTerminalInfoList({
    merchantNo: routeParams.merchantNo,
  });
  terminalInfo.value = Object.assign([], data);
}

async function queryTotalTransInfByMerchNo() {
  const data = await MerchApi.queryTotalTransInfByMerchNo({
    merchantNo: routeParams.merchantNo,
  });
  totalTransInfo.value = Object.assign({}, data);
}

async function queryTotalTransInfByMutiCondition() {
  const data = await MerchApi.queryTotalTransInfByMutiCondition({
    merchantNo: routeParams.merchantNo,
    ...monthTransInfoWhere,
  });
  monthTransInfo.value = Object.assign({}, data);
  monthTransInfo.value.totalTransAmt = NP.plus(Number(data.debitCardTotalTransAmt), Number(data.creditCardTotalTransAmt));
  monthTransInfo.value.wechatTotalTransAmt = NP.plus(Number(data.wechatCreditTotalTransAmt), Number(data.wechatDebitTotalTransAmt));
  monthTransInfo.value.alipayTotalTransAmt = NP.plus(Number(data.alipayCreditTotalTransAmt), Number(data.alipayDebitTotalTransAmt));
  monthTransInfo.value.unionpayTotalTransAmt = NP.plus(Number(data.unionpayCreditTotalTransAmt), Number(data.unionpayDebitTotalTransAmt));
}

const reportRespCodesMap: Record<string, any> = {
  1: '审核中',
  2: '入网成功',
  3: '重新提交',
  99: '已关闭',
  10: '电子协议待签署',
  15: '待用户选择结算卡',
};

// 通道开通状态映射
const channelOpenStatusMap: Record<number, string> = {
  '-1': '暂不支持',
  '0': '未开通',
  '1': '已开通',
  '3': '开通失败',
};
// 银联开通状态映射
const unionpayOpenStatusMap: Record<number, string> = {
  '-1': '暂不支持',
  '0': '未开通',
  '1': '已开通',
  '2': '开通中',
  '3': '开通失败',
  '4': '开通中',
  '11': '开通中',
};

type ChannelType = 'union' | 'wechat' | 'alipay';
function formatTagDesc(type: ChannelType, status: number) {
  if (status === null) {
    return '--';
  }
  switch (type) {
    case 'union':
      return `银联${unionpayOpenStatusMap[status]}`;
    case 'wechat':
      return `微信${channelOpenStatusMap[status]}`;
    case 'alipay':
      return `支付宝${channelOpenStatusMap[status]}`;
    default:
      return '--';
  }
}

function toModifyRate(item: any) {
  const url = buildUrlWithParams('/pages-org/merch-report/modify-rate', {
    chlMerchantNo: item.chlMerchantNo,
    policyNameDesc: item.policyNameDesc,
  });
  uni.navigateTo({ url });
}

function copyData(data: string) {
  useClipboard().setClipboardData({ data });
}

type StatusMap = Record<number, string>;
// 终端绑定状态
const unionSnBindStatusMap: StatusMap = {
  0: '未绑定',
  1: '已绑定',
  2: '绑定失败',
};
// 银联终端报备状态
const unionSnReportStatusMap: StatusMap = {
  0: '未报备',
  1: '已报备',
  2: '报备失败',
};

function toTerminalDetail(terminalSn: string) {
  const url = buildUrlWithParams('/pages-org/merch/terminal-detail', {
    terminalSn,
    merchantNo: routeParams.merchantNo,
  });
  uni.navigateTo({ url });
}
</script>

<style lang="scss" scoped>
.cell-group{
  .cell{
    @apply flex items-center;

    &:not(:last-child){
     @apply mb-8rpx
    }

     .cell-label{
      @apply shrink-0;
     }

     .cell-value{
      @apply grow ml-20rpx flex items-center text-#333;
     }
  }
}

:deep(.custom-tag-class){
  margin-right: 10rpx;
  font-size: 26rpx !important;
  color: #4d80f0 !important;
  background: #d0e8ff !important;
}

:deep(.custom-cell-group) {
  .wd-cell {
    @apply p-0;
  }

  .wd-cell__wrapper {
    @apply p-2px;
  }
}

.copy-icon{
  @apply i-mdi-content-copy  size-28rpx text-#b51e1e ml-2px shrink-0;
}
</style>
