<template>
  <view>
    <wd-form ref="formRef" :model="form" :rules="rules" error-type="toast">
      <wd-cell-group border>
        <wd-cell
          title="回拨对象" title-width="100px" prop="agentNo" is-link
          @click="onSelectAgent"
        >
          <wd-textarea
            v-model="form.agentName"
            placeholder="请选择回拨代理商"
            auto-height no-border readonly
            custom-textarea-class="text-right"
          />
        </wd-cell>
        <wd-input
          v-model="form.terminalSnStart"
          prop="terminalSnStart"
          label="起始SN号"
          label-width="100px"
          placeholder="请输入起始SN号"
          clearable use-suffix-slot align-right
        >
          <template #suffix>
            <i
              class="i-mdi-barcode-scan text-#4d80f0" size-36rpx
              @click="scanCode('terminalSnStart')"
            />
          </template>
        </wd-input>
        <wd-input
          v-model="form.terminalSnEnd"
          prop="terminalSnEnd"
          label="截止SN号"
          label-width="100px"
          placeholder="请输入截止SN号"
          clearable use-suffix-slot align-right
        >
          <template #suffix>
            <i
              class="i-mdi-barcode-scan text-#4d80f0" size-36rpx
              @click="scanCode('terminalSnEnd')"
            />
          </template>
        </wd-input>
        <wd-cell />
      </wd-cell-group>

      <view class="mt40px px-40rpx">
        <wd-button type="primary" size="large" block @click="save">
          确定
        </wd-button>
      </view>
    </wd-form>

    <!-- H5扫码组件 -->
    <!-- #ifdef H5 -->
    <cshaptx4869-scancode
      v-if="showH5ScanCode"
      @success="onScanSuccess"
      @fail="onScanFail"
      @close="onScanClose"
    />
    <!-- #endif -->

    <!-- 挂载点 -->
    <wd-toast />
  </view>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'wot-design-uni/components/wd-form/types';
import { TerminalManageApi } from '@/api-org/terminal-manage';
import { emitter } from '@/utils/emitter';
import { NavigationHelper } from '@/utils';

// 表单
const formRef = ref<FormInstance | null>(null);
const form = reactive<any>({
  agentNo: '', // 回拨直属下级代理编码（必填）
  terminalSnStart: '', // 终端开始序列号(必填)
  terminalSnEnd: '', // 终端截止序列号(必填)
});
// 规则
const rules: FormRules = {
  agentNo: [{ required: true, message: '请选择回拨对象' }],
  terminalSnStart: [{ required: true, message: '请输入起始SN号' }],
  terminalSnEnd: [{ required: true, message: '请输入截止SN号' }],
};

const terminalSnStartOrEnd = ref('');

const showH5ScanCode = ref(false);

function onSelectAgent() {
  uni.navigateTo({ url: '/pages-org/picker-view/target-agent' });
  emitter.on('picker-target-agent', (item) => {
    form.agentNo = item.agentNo;
    form.agentName = item.agentName;
  });
}

async function save() {
  // 检验表单
  const { valid, errors } = await formRef.value!.validate();
  if (!valid)
    return Promise.reject(errors);

  const data = await TerminalManageApi.callbackTerminalList({
    terminalSnStart: form.terminalSnStart,
    terminalSnEnd: form.terminalSnEnd,
  });

  const filterData = data?.map((item: any) => ({ terminalSn: item.terminalSn })) || [];

  NavigationHelper.navigateToWithData('/pages-org/terminal-manage/terminal-callback/terminal-callback-confirm', {
    agentNo: form.agentNo,
    terminalSnList: filterData,
  });
}

function scanCode(type: 'terminalSnStart' | 'terminalSnEnd') {
  terminalSnStartOrEnd.value = type;
  // #ifdef H5
  showH5ScanCode.value = true;
  // #endif
  // #ifndef H5
  uni.scanCode({
    success: (res) => {
      form[terminalSnStartOrEnd.value] = res.result || '';
    },
  });
  // #endif
}

// #region h5扫码相关
function onScanSuccess(res: string) {
  showH5ScanCode.value = false;
  form[terminalSnStartOrEnd.value] = res || '';
}
function onScanFail(err: { errName: string;errMsg: string }) {
  uni.showModal({
    title: '提示',
    content: '浏览器不支持, 请您手动输入或切换浏览器重试哦~',
    showCancel: false,
    complete: () => {
      showH5ScanCode.value = false;
    },
  });
  console.error(err.errName, err.errMsg);
}
function onScanClose() {
  showH5ScanCode.value = false;
}
// #endregion
</script>
