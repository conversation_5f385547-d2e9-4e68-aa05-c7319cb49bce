<template>
  <view class="h-full overflow-y-scroll">
    <view class="h-full flex flex-col">
      <view class="shrink-0 bg-#fff6c8 px-20rpx py-16rpx">
        <text class="text-24rpx text-#f0883a">
          预计回拨机具{{ form.terminalSnList.length }}台
        </text>
      </view>

      <view class="flex-1 overflow-y-scroll">
        <wd-checkbox-group v-model="form.terminalSnList" cell border>
          <wd-checkbox v-for="item in terminalSnOptions" :key="item.terminalSn" :model-value="item.terminalSn">
            SN: {{ item.terminalSn }}
          </wd-checkbox>
        </wd-checkbox-group>
      </view>

      <view class="shrink-0 p-40rpx">
        <wd-button type="primary" size="large" block @click="save">
          回拨确认
        </wd-button>
      </view>
      <view />
    </view>
  </view>
</template>

<script setup lang="ts">
import { useToast } from 'wot-design-uni';
import { TerminalManageApi } from '@/api-org/terminal-manage';
import { NavigationHelper } from '@/utils';

const toast = useToast();

const form = reactive<any>({
  terminalSnList: [], // 终端序列号列表必填
  agentNo: '', // 回拨直属下级代理编码（必填）
});

const terminalSnOptions = ref<any[]>([]);

onLoad((query: any) => {
  const { transferredData, hasTransferredData } = NavigationHelper.getTransferredData(query);

  if (hasTransferredData) {
    terminalSnOptions.value = transferredData.terminalSnList || [];

    form.agentNo = transferredData.agentNo || '';
    form.terminalSnList = terminalSnOptions.value.map((item: any) => item.terminalSn);
  }
});

async function save() {
  if (!form.terminalSnList.length) {
    toast.error({
      msg: '请选择回拨机具',
    });
    return;
  }

  await TerminalManageApi.callbackTerminalBySn(form);

  uni.navigateTo({ url: '/pages-org/terminal-manage/transfer-record/index' });
}
</script>
