<template>
  <view class="h-full overflow-hidden bg-primary">
    <view class="h-full flex flex-col">
      <!-- 顶部固定 -->
      <view class="shrink-0">
        <wd-tabs v-model="transferType" @change="onChangeTransferType">
          <wd-tab title="选择下发" />
          <wd-tab title="号段下发" />
        </wd-tabs>

        <view class="my-1px">
          <wd-cell
            title="下发对象" title-width="100px" prop="agentNo" is-link
            @click="onSelectAgent"
          >
            <wd-textarea
              v-model="form.agentName"
              placeholder="请选择下发代理商"
              auto-height no-border readonly
              custom-textarea-class="text-right"
            />
          </wd-cell>
        </view>
      </view>
      <!-- 选择下发 分页列表 -->
      <view v-if="transferType === 0" class="grow">
        <page-paging ref="pagingRef" v-model="datasource" :default-page-size="30" :fixed="false" @query="queryDataSource">
          <!-- 头部 -->
          <template #top>
            <wd-search
              hide-cancel placeholder-left placeholder="请输入机具SN号" custom-class="w-full"
              @search="onSearchByTerminalSn"
            />

            <view class="mt-1px">
              <!-- 普通终端下发筛选条件 -->
              <wd-drop-menu v-if="transferTerminalType === 0">
                <wd-drop-menu-item
                  v-model="where.modelId" :options="modelIdOptions" :[modelIdTitleProp]="'机具型号'"
                  @change="reload"
                />
                <wd-drop-menu-item
                  v-model="where.serviceFeeId" :options="serviceFeeIdOptions" :[serviceFeeIdTitleProp]="'激活政策'"
                  @change="reload"
                />
                <wd-drop-menu-item
                  v-model="where.policyId" :options="policyIdOptions" :[policyIdTitleProp]="'费率政策'"
                  @change="reload"
                />
              </wd-drop-menu>

              <!-- 活动终端下发筛选条件 -->
              <wd-drop-menu v-else-if="transferTerminalType === 1">
                <wd-drop-menu-item
                  v-model="where.activePolicyNo" :options="activePolicyNoOptions" :[activePolicyNoTitleProp]="'激活奖励政策'"
                  @change="reload"
                />
                <wd-drop-menu-item
                  v-model="where.cashbackPolicyNo" :options="cashbackPolicyNoOptions" :[cashbackPolicyNoTitleProp]="'达标奖励政策'"
                  @change="reload"
                />
              </wd-drop-menu>
            </view>
          </template>

          <!-- 列表 -->
          <view class="mt-10px">
            <wd-checkbox-group v-model="form.terminalSnList" cell border>
              <view v-for="(item, key) in datasource" :key="key" class="flex items-center border-t-#f8f8f8 border-t-solid">
                <wd-checkbox :model-value="item.terminalSn">
                  SN: {{ item.terminalSn }}
                </wd-checkbox>
                <i
                  v-if="item.terminalSn"
                  class="copy-icon"
                  @click.stop="copyData(item.terminalSn)"
                />
              </view>
            </wd-checkbox-group>
          </view>

          <!-- 底部 -->
          <template #bottom>
            <view class="border-t-#f8f8f8 border-t-solid bg-white px-30rpx pb-40rpx pt-20rpx">
              <view class="flex items-center">
                <view class="shrink-0">
                  <wd-checkbox v-model="isCheckedAll">
                    全选
                  </wd-checkbox>
                </view>
                <view class="mx-20rpx grow break-all text-center text-#4d80f0">
                  已选{{ form.terminalSnList?.length || 0 }}台
                </view>
                <view class="flex shrink-0 items-center">
                  <wd-button size="small" @click="handleTransferBySelect">
                    下发终端
                  </wd-button>
                </view>
              </view>
            </view>
          </template>
        </page-paging>
      </view>

      <!-- 号段下发 -->
      <view v-else-if="transferType === 1" class="mt-10px flex grow flex-col">
        <view class="grow">
          <wd-cell-group border>
            <wd-input
              v-model="form.terminalSnStart"
              prop="terminalSnStart"
              label="起始SN号"
              label-width="100px"
              placeholder="请输入起始SN号"
              clearable use-suffix-slot align-right
            >
              <template #suffix>
                <i
                  class="i-mdi-barcode-scan text-#4d80f0" size-36rpx
                  @click="scanCode('terminalSnStart')"
                />
              </template>
            </wd-input>
            <wd-input
              v-model="form.terminalSnEnd"
              prop="terminalSnEnd"
              label="截止SN号"
              label-width="100px"
              placeholder="请输入截止SN号"
              clearable use-suffix-slot align-right
            >
              <template #suffix>
                <i
                  class="i-mdi-barcode-scan text-#4d80f0" size-36rpx
                  @click="scanCode('terminalSnEnd')"
                />
              </template>
            </wd-input>
          </wd-cell-group>
        </view>
        <view class="shrink-0">
          <view class="p-40rpx">
            <wd-button type="primary" size="large" block @click="handleTransferByNumberSegment">
              确定
            </wd-button>
          </view>
        </view>
      </view>
    </view>

    <!-- 挂载点 -->
    <wd-message-box />
  </view>
</template>

<script setup lang="ts">
import { useMessage } from 'wot-design-uni';
import { useClipboard } from '@/hooks';
import { RatePolicyApi } from '@/api-org/rate-policy';
import { NavigationHelper, deepClone } from '@/utils';
import { emitter } from '@/utils/emitter';
import { TerminalManageApi } from '@/api-org/terminal-manage';
import { CHANNEL_CODE } from '@/config/setting';

const message = useMessage();

// 分页器ref
const pagingRef = ref();

// 0 普通下发; 1 活动下发
const transferTerminalType = ref();

// 列表查询条件
const where = reactive<any>({
  termActiveSwitch: null,
  terminalSn: '',
  modelId: -1,
  serviceFeeId: -1,
  policyId: -1,
  // 活动终端特有
  activePolicyNo: -1,
  cashbackPolicyNo: -1,
});

// 列表数据
const datasource = ref<Record<string, any>[]> ([]);

const form = reactive<any>({
  terminalSnList: [],
});

// 0 选择下发; 1 号段下发
const transferType = ref(0);

// 全选/反选
const isCheckedAll = computed({
  get() {
    return form.terminalSnList.length && form.terminalSnList.length === datasource.value.length;
  },
  set(val) {
    if (val) {
      form.terminalSnList = datasource.value.map((item: any) => item.terminalSn);
    }
    else {
      form.terminalSnList = [];
    }
  },
});

const policyIdOptions = ref<any>([
  { label: '全部', value: -1 },
]);
const policyIdTitleProp = computed(() => {
  const prop = where.policyId === -1 ? 'title' : '';
  return prop;
});

const modelIdOptions = ref<any>([
  { label: '全部', value: -1 },
]);
const modelIdTitleProp = computed(() => {
  const prop = where.modelId === -1 ? 'title' : '';
  return prop;
});

const serviceFeeIdOptions = ref<any>([
  { label: '全部', value: -1 },
]);
const serviceFeeIdTitleProp = computed(() => {
  const prop = where.serviceFeeId === -1 ? 'title' : '';
  return prop;
});

const activePolicyNoOptions = ref<any>([
  { label: '全部', value: -1 },
]);
const activePolicyNoTitleProp = computed(() => {
  const prop = where.activePolicyNo === -1 ? 'title' : '';
  return prop;
});

const cashbackPolicyNoOptions = ref<any>([
  { label: '全部', value: -1 },
]);
const cashbackPolicyNoTitleProp = computed(() => {
  const prop = where.cashbackPolicyNo === -1 ? 'title' : '';
  return prop;
});

onLoad((query) => {
  transferTerminalType.value = Number(query?.terminalType);
  where.termActiveSwitch = transferTerminalType.value;

  init();
});

function init() {
  if (transferTerminalType.value === 0) {
    queryRatePolicy();
    queryModels();
    queryServiceFee();
  }
  else if (transferTerminalType.value === 1) {
    queryOwnerCashPolicyInfo();
  }
}

function handleTransferBySelect() {
  if (!form.agentNo) {
    message.alert({
      title: '请选择下发代理商',
    });
    return;
  }

  if (!form.terminalSnList.length) {
    message.alert({
      title: '请选择下发终端',
    });
    return;
  }

  const filterData = form.terminalSnList.map((item: any) => ({ terminalSn: item })) || [];

  NavigationHelper.navigateToWithData('/pages-org/terminal-manage/terminal-delivery/terminal-delivery-confirm', {
    termActiveSwitch: transferTerminalType.value,
    agentNo: form.agentNo,
    terminalSnList: filterData,
    optTerminalType: transferType.value === 0 ? 1 : 0,
  });
}

async function handleTransferByNumberSegment() {
  if (!form.agentNo) {
    message.alert({
      title: '请选择下发代理商',
    });
    return;
  }

  if (!form.terminalSnStart) {
    message.alert({
      title: '请输入起始SN号',
    });
    return;
  }

  if (!form.terminalSnEnd) {
    message.alert({
      title: '请输入截止SN号',
    });
    return;
  }

  const data = await TerminalManageApi.queryTerminalList({
    termActiveSwitch: transferTerminalType.value,
    terminalSnStart: form.terminalSnStart,
    terminalSnEnd: form.terminalSnEnd,
  });

  const filterData = data?.map((item: any) => ({ terminalSn: item.terminalSn })) || [];

  NavigationHelper.navigateToWithData('/pages-org/terminal-manage/terminal-delivery/terminal-delivery-confirm', {
    termActiveSwitch: transferTerminalType.value,
    agentNo: form.agentNo,
    terminalSnList: filterData,
    optTerminalType: transferType.value === 0 ? 1 : 0,
    terminalSnStart: form.terminalSnStart,
    terminalSnEnd: form.terminalSnEnd,
  });
}

function onChangeTransferType() {
  form.terminalSnStart = '';
  form.terminalSnEnd = '';
  form.terminalSnList = [];
}
async function queryOwnerCashPolicyInfo() {
  let data = await TerminalManageApi.fetchOwnerCashPolicyInfo();
  data = data || {};

  let { activeCashRuleList, reachCashRuleList } = data;
  activeCashRuleList = activeCashRuleList || [];
  reachCashRuleList = reachCashRuleList || [];

  const formatActiveCashRuleList = activeCashRuleList.map((item: any) => {
    return { label: item.policyName, value: item.policyNo };
  });

  const formatReachCashRuleList = reachCashRuleList.map((item: any) => {
    return { label: item.policyName, value: item.policyNo };
  });

  activePolicyNoOptions.value = [activePolicyNoOptions.value[0], ...formatActiveCashRuleList];
  cashbackPolicyNoOptions.value = [cashbackPolicyNoOptions.value[0], ...formatReachCashRuleList];
}

async function queryModels() {
  let data = await TerminalManageApi.terminalModelList({});
  data = data || [];

  const formatData = data.map((item: any) => {
    return {
      label: item.modelName,
      value: item.id,
    };
  });
  modelIdOptions.value = [modelIdOptions.value[0], ...formatData];
}

async function queryRatePolicy() {
  let data = await RatePolicyApi.getRatePolicylistOfOrg(
    {
      policyType: 2, // 政策类型  2-商户费率政策 (必填)
      channelCode: CHANNEL_CODE, // 通道编码 （policyType = 2时，必填）
    },
  );
  data = data || [];

  const formatData = data.map((item: any) => {
    return {
      label: item.policyDesc,
      value: item.id,
    };
  });

  policyIdOptions.value = [policyIdOptions.value[0], ...formatData];
}

async function queryServiceFee() {
  let data = await TerminalManageApi.serviceFeePolicySelfOpenList();
  data = data || [];

  const formatData = data.map((item: any) => {
    return {
      label: item.policyName,
      value: item.configId,
    };
  });
  serviceFeeIdOptions.value = [serviceFeeIdOptions.value[0], ...formatData];
}

function onSelectAgent() {
  uni.navigateTo({ url: '/pages-org/picker-view/target-agent' });
  emitter.on('picker-target-agent', (item) => {
    form.agentNo = item.agentNo;
    form.agentName = item.agentName;
  });
}

/** 搜索数据 */
function onSearchByTerminalSn({ value }: any) {
  where.terminalSn = value;
  reload();
}

function reload() {
  form.terminalSnList = [];
  pagingRef.value?.reload();
}

function copyData(data: string) {
  useClipboard().setClipboardData({ data });
}

/** 查询数据 */
function queryDataSource(pageNo: number, pageSize: number) {
  const formatWhere = deepClone(where);

  formatWhere.modelId = formatWhere.modelId === -1 ? null : formatWhere.modelId;
  formatWhere.serviceFeeId = formatWhere.serviceFeeId === -1 ? null : formatWhere.serviceFeeId;
  formatWhere.policyId = formatWhere.policyId === -1 ? null : formatWhere.policyId;
  formatWhere.activePolicyNo = formatWhere.activePolicyNo === -1 ? null : formatWhere.activePolicyNo;
  formatWhere.cashbackPolicyNo = formatWhere.cashbackPolicyNo === -1 ? null : formatWhere.cashbackPolicyNo;

  TerminalManageApi.selefIdleTerminal({ ...formatWhere, pageNo, pageSize })
    .then((res) => {
      pagingRef.value.completeByTotal(res?.rows, res?.totalRows);
    })
    .catch(() => {
      pagingRef.value.completeByTotal(false);
    });
}
</script>

<style lang="scss" scoped>
.search-box {
  display: flex;
  align-items: center;
  height: 100%;
  text-align: left;

  --wot-search-padding: 0;
  --wot-search-side-padding: 0;
}

.cell{
    @apply flex items-center;

    &:not(:last-child){
     @apply mb-8rpx
    }

     .cell-label{
      @apply shrink-0;
     }

     .cell-value{
      @apply grow ml-20rpx flex items-center text-#333;
     }
  }

:deep(.custom-tag-class){
  margin-right: 10rpx;
  font-size: 26rpx !important;
  color: #4d80f0 !important;
  background: #d0e8ff !important;
}

:deep(.custom-navbar-class){
  padding-top: 8px;

  .wd-navbar__title{
    padding: 0 12px 0 44px;
    margin: 0;
    max-width: 100%;
  }
}

.lr-border{
  position: relative;
  overflow: hidden;

  &::before,
  &::after{
   position: absolute;
   top: 0;
   width: 1px;
   height:60%;
   background: #e6e6e6;
   content: '';
   transform: translateY(50%);
  }

  &::before{
   left: 0;

  }

  &::after{
   right: 0;
  }

}

.copy-icon{
  @apply i-mdi-content-copy ml--7px size-28rpx text-#b51e1e
}
</style>
