<template>
  <view class="h-full overflow-y-scroll">
    <view class="h-full flex flex-col">
      <view class="shrink-0 bg-#fff6c8 px-20rpx py-16rpx">
        <text class="text-24rpx text-#f0883a">
          预计下发机具{{ form.terminalSnList.length }}台
        </text>
      </view>

      <view class="flex-1 overflow-y-scroll">
        <wd-checkbox-group v-model="form.terminalSnList" cell border>
          <wd-checkbox v-for="(item, key) in terminalSnOptions" :key="key" :model-value="item.terminalSn">
            SN: {{ item.terminalSn }}
          </wd-checkbox>
        </wd-checkbox-group>
      </view>

      <view class="shrink-0 p-40rpx">
        <wd-button type="primary" size="large" block @click="save">
          下发确认
        </wd-button>
      </view>
      <view />
    </view>
  </view>

  <wd-toast />
</template>

<script setup lang="ts">
import { useToast } from 'wot-design-uni';
import { NavigationHelper } from '@/utils/transfer/index';

const toast = useToast();

const form = reactive<any>({
  terminalSnList: [], // 终端序列号列表必填
  agentNo: '', // 下发直属下级代理编码（必填）
  termActiveSwitch: '', // 是否活动终端 0 否; 1 是
});

const terminalSnOptions = ref<any[]>([]);

onLoad((query: any) => {
  const { transferredData, hasTransferredData } = NavigationHelper.getTransferredData(query);

  if (hasTransferredData) {
    terminalSnOptions.value = transferredData.terminalSnList || [];
    form.agentNo = transferredData.agentNo || '';
    form.terminalSnList = terminalSnOptions.value.map((item: any) => item.terminalSn);
    form.termActiveSwitch = transferredData.termActiveSwitch;
    form.optTerminalType = transferredData.optTerminalType;
    form.terminalSnStart = transferredData.terminalSnStart;
    form.terminalSnEnd = transferredData.terminalSnEnd;
  }
});

function save() {
  if (!form.terminalSnList.length) {
    toast.info({
      msg: '请选择下发机具',
    });
    return;
  }

  NavigationHelper.navigateToWithData('/pages-org/terminal-manage/terminal-delivery/rate-config', {
    agentNo: form.agentNo,
    terminalSnList: form.terminalSnList,
    termActiveSwitch: form.termActiveSwitch,
    optTerminalType: form.optTerminalType,
    terminalSnStart: form.terminalSnStart,
    terminalSnEnd: form.terminalSnEnd,
  });
}
</script>
