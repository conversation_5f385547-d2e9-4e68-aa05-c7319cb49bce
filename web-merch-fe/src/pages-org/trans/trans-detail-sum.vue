<template>
  <view class="h-full bg-primary">
    <page-paging ref="pagingRef" v-model="datasource" @query="queryDataSource">
      <!-- 头部 -->
      <template #top>
        <view v-if="[3, 5].includes(loginUser.orgType as number)" class="mb-5px">
          <wd-tabs v-model="where.selType" @change="onChangeWhereSelType">
            <wd-tab title="全部" name="3" />
            <wd-tab title="自营" name="1" />
            <wd-tab title="团队" name="2" />
          </wd-tabs>
        </view>

        <view class="bg-white">
          <wd-drop-menu custom-class="flex">
            <wd-drop-menu-item
              v-model="where.transStatus" :options="transStatusOptions"
              :[transStatusTitleProp]="'交易状态'"
              @change="onChangeWhereStatus"
            />
            <wd-drop-menu-item
              v-model="where.payMethod" :options="payMethodOptions"
              :[payMethodTitleProp]="'交易方式'"
              @change="onChangeWherePayMethod"
            />
          </wd-drop-menu>
        </view>

        <view class="p-20rpx">
          <view class="flex flex-col items-start rounded-md bg-#4D80F0 p-20rpx text-28rpx text-white">
            <!-- 查询条件 -->
            <view class="flex items-center">
              <custom-datetime-picker
                v-model="whereDate"
                type="date"
                :default-value="whereDate"
                custom-value-class="!text-white"
                @confirm="handleConfirmDate"
              />
            </view>

            <!-- 数据信息 -->
            <view class="mt-10rpx w-full flex flex items-center">
              <view class="flex grow flex-col">
                <text>交易金额(元)</text>
                <text>{{ transInfo.transTotalAmt }}</text>
              </view>
              <view class="w-20rpx shrink-0" />
              <view class="flex grow flex-col items-end">
                <text>交易笔数</text>
                <text>{{ transInfo.transTotalNum }}</text>
              </view>
            </view>
          </view>
        </view>
      </template>

      <view v-for="(item, key) in datasource" :key="key" class="bg-white" @click="toDetail(item)">
        <view class="flex items-center border border-#f3f5f7 border-b-solid py-20rpx">
          <view class="shrink-0">
            <i class="i-mdi-storefront size-70rpx shrink-0 text-#846a27" />
          </view>
          <view class="flex grow flex-col">
            <view class="flex grow">
              <view class="flex basis-17/24 flex-col break-all px-20rpx text-29rpx">
                <text class="mb-4px font-500">
                  {{ item.merchantName }}
                </text>
                <view class="mb-4px flex items-center">
                  <text>{{ item.chlMerchantNo }}</text>
                  <i v-if="item.chlMerchantNo" class="copy-icon" @click.stop="copyContent(item.chlMerchantNo)" />
                </view>
                <view class="mb-4px flex items-center">
                  <text>{{ item.terminalSn }}</text>
                  <i v-if="item.terminalSn" class="copy-icon" @click.stop="copyContent(item.terminalSn)" />
                </view>
                <text v-if="item.teamFlag === 1" class="mb-4px">
                  {{ item.directTeamName }}
                </text>
                <text class="mb-4px text-#999">
                  {{ item.createTime }}
                </text>
              </view>

              <view class="flex shrink-0 basis-7/24 items-center justify-end">
                <view class="flex flex-col items-end break-all">
                  <text class="mb-10rpx text-32rpx font-500">
                    {{ item.transAmount }}
                  </text>
                  <template v-for="(option, optionKey) in transStatusOptions" :key="optionKey">
                    <text v-if="item.transStatus === option.value" :class="option.textClass" class="text-28rpx">
                      {{ option.label }}
                    </text>
                  </template>
                </view>
                <i class="i-mdi-chevron-right size-60rpx text-#666" />
              </view>
            </view>

            <view class="px-20rpx">
              <wd-tag type="primary" plain custom-class="custom-tag-class">
                {{ payCardTypeMap[item.payCardType] || '--' }}
              </wd-tag>
              <wd-tag type="primary" plain custom-class="custom-tag-class">
                {{ feeTypeMap[item.feeType] || '--' }}
              </wd-tag>
            </view>
          </view>
        </view>
      </view>
    </page-paging>
  </view>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { buildUrlWithParams, deepClone } from '@/utils';
import { TransApi } from '@/api-org/trans';
import { useClipboard } from '@/hooks';
import { useUserStore } from '@/store';

const loginUser = computed(() => useUserStore().info);

const pagingRef = ref();

const whereStartDateDef = dayjs().startOf('month').valueOf();
const whereEndDateDef = dayjs().valueOf();
const whereDate = ref([whereStartDateDef, whereEndDateDef]);

const where = reactive<any>({
  selType: '3', // 查询类型 1-自营  2-团队 3-全部
  selTimeType: 1, // 查询类型 必填 0-根据月份查询 1-自定义时间查询 2-快捷查询
  transStatus: -1, // 交易状态 1-已创建 2-交易成功 3-交易失败 4-支付中 5-预下单成功 6-结果待查询
  payMethod: -1, // 交易方式 1-银联云闪付 2-微信支付 3-支付宝支付 4-EPOS支付 5-POS刷卡
  queryBeginTime: dayjs(whereStartDateDef).format('YYYY-MM-DD'), // 开始时间 yyyy-MM-dd
  queryEndTime: dayjs(whereEndDateDef).format('YYYY-MM-DD'), // 结束时间 yyyy-MM-dd
});

const transStatusTitleProp = computed(() => {
  const prop = where.transStatus === -1 ? 'title' : '';
  return prop;
});

const payMethodTitleProp = computed(() => {
  const prop = where.payMethod === -1 ? 'title' : '';
  return prop;
});

const datasource = ref<any>([]);

const transInfo = ref<any>({
  transTotalAmt: '0.00', // 交易总金额（单位：元）
  transTotalNum: '0', // 交易总笔数
});

const transStatusOptions = [
  { label: '全部', value: -1 },
  { label: '已创建', value: 1, textClass: 'text-blue' },
  { label: '交易成功', value: 2, textClass: 'text-green' },
  { label: '交易失败', value: 3, textClass: 'text-red' },
  { label: '进行中', value: 4, textClass: 'text-orange' },
  { label: '已受理', value: 5, textClass: 'text-blue' },
  { label: '支付结果待查', value: 6, textClass: 'text-blue' },
];

const payMethodOptions = [
  { label: '全部', value: -1 },
  { label: '银联云闪付', value: 1 },
  { label: '微信支付', value: 2 },
  { label: '支付宝支付', value: 3 },
  { label: 'Epos支付', value: 4 },
  { label: 'POS刷卡', value: 5 },
];

const payCardTypeMap: EnumMap = {
  1: '借记卡',
  2: '贷记卡',
  3: '准贷记卡',
  4: '预付费卡',
};

const feeTypeMap: EnumMap = {
  B: '标准费率',
  YN: '云闪付费率',
};

onLoad((query) => {
  if (query?.transDayTime) {
    const formatTime = dayjs(query.transDayTime).format('YYYY-MM-DD');
    where.queryBeginTime = formatTime;
    where.queryEndTime = formatTime;
    whereDate.value = [dayjs(formatTime).valueOf(), dayjs(formatTime).valueOf()];
  }
});

function handleConfirmDate({ value }: { value: string[] }) {
  [where.queryBeginTime, where.queryEndTime] = value;
  pagingRef.value.reload();
}

function onChangeWhereSelType() {
  pagingRef.value.reload();
}

function onChangeWhereStatus() {
  pagingRef.value.reload();
}

function onChangeWherePayMethod() {
  pagingRef.value.reload();
}

function toDetail(item: any) {
  const url = buildUrlWithParams('/pages-org/trans/trans-detail', {
    id: item.id,
  });
  uni.navigateTo({ url });
}

function copyContent(data: string) {
  useClipboard().setClipboardData({ data });
}

function queryDataSource(pageNo: number, pageSize: number) {
  const formatWhere = deepClone(where);
  formatWhere.transStatus = where.transStatus === -1 ? null : where.transStatus;
  formatWhere.payMethod = where.payMethod === -1 ? null : where.payMethod;

  TransApi.queryTransDetailInfoByPage({ ...formatWhere, pageNo, pageSize })
    .then((res) => {
      const { transInfoSumResponse, pageResult } = res || {};

      transInfo.value = Object.assign(transInfo.value, transInfoSumResponse);

      pagingRef.value.completeByTotal(pageResult?.rows, pageResult?.totalRows);
    })
    .catch(() => {
      pagingRef.value.completeByTotal(false);
    });
}
</script>

<style lang="scss" scoped>
:deep(.custom-tag-class){
  margin-right: 10px;
  font-size: 24rpx !important;
}

.copy-icon{
  @apply i-mdi-content-copy  size-28rpx text-#b51e1e ml-2px shrink-0;
}
</style>
