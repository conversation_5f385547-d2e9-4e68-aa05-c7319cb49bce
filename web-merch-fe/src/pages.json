{

  "pages": [
    {
      "path": "pages/advance/index",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/tab/home/<USER>",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/tab/statistic/index",
      "style": {
        "navigationBarTitleText": "数据统计",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/tab/user/index",
      "style": {
        "navigationStyle": "custom"
      }
    }
  ],
  "subPackages": [
    {
      "root": "pages/common",
      "pages": [
        {
          "path": "login/index",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "register/index",
          "style": {
            "navigationBarTitleText": "新用户注册"
          }
        },
        {
          "path": "tourist/index",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "webview/index",
          "style": {
            "navigationBarTitleText": ""
          }
        },
        {
          "path": "notice/index",
          "style": {
            "navigationBarTitleText": "消息中心"
          }
        },
        {
          "path": "notice/notice-detail",
          "style": {
            "navigationBarTitleText": "消息详情"
          }
        }
      ]
    },
    {
      "root": "pages/picker-view",
      "pages": [
        {
          "path": "category/micro-category",
          "style": {
            "navigationBarTitleText": "类目选择"
          }
        },
        {
          "path": "category/channel-category",
          "style": {
            "navigationBarTitleText": "类目选择"
          }
        },
        {
          "path": "category/mcc-category",
          "style": {
            "navigationBarTitleText": "MCC选择"
          }
        },
        {
          "path": "area/index",
          "style": {
            "navigationBarTitleText": "地区选择",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "bank/bank-type",
          "style": {
            "navigationBarTitleText": "总行选择"
          }
        },
        {
          "path": "bank/bank-sub",
          "style": {
            "navigationBarTitleText": "支行选择"
          }
        }
      ]
    },
    {
      "root": "pages/report",
      "pages": [
        {
          "path": "merch-auth/auth-type-picker-view",
          "style": {
            "navigationBarTitleText": "类型选择"
          }
        },
        {
          "path": "merch-auth/auth-micro-merch/index",
          "style": {
            "navigationBarTitleText": "实名认证",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "merch-auth/auth-result",
          "style": {
            "navigationBarTitleText": "审批结果"
          }
        },
        // #ifdef H5
        {
          "path": "merch-auth/auth-company-merch",
          "style": {
            "navigationBarTitleText": "企业信息认证"
          }
        },
        // #endif
        // #ifdef H5
        {
          "path": "merch-auth/address-prompt",
          "style": {
            "navigationBarTitleText": "详细地址",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "merch-auth/map-poi",
          "style": {
            "navigationBarTitleText": "详细地址",
            "navigationStyle": "custom"
          }
        },
        // #endif
        {
          "path": "merch-report/index",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "merch-report/merch-report-detail",
          "style": {
            "navigationBarTitleText": "商户详情"
          }
        },
        {
          "path": "merch-report/upgrade-corporate-merch",
          "style": {
            "navigationBarTitleText": "报备个体商户"
          }
        },
        {
          "path": "merch-report/modify-merch-name",
          "style": {
            "navigationBarTitleText": "修改商户简称"
          }
        },
        {
          "path": "merch-report/merch-open-d0",
          "style": {
            "navigationBarTitleText": "开通D0"
          }
        },
        {
          "path": "merch-dredge/index",
          "style": {
            "navigationBarTitleText": ""
          }
        },
        {
          "path": "merch-dredge/terminal-entry",
          "style": {
            "navigationBarTitleText": "设备绑定"
          }
        },
        {
          "path": "merch-signature/index",
          "style": {
            "navigationBarTitleText": "签名"
          }
        },
        {
          "path": "merch-signature/protocol/index",
          "style": {
            "navigationBarTitleText": "协议"
          }
        },
        {
          "path": "merch-signature/appendix/index",
          "style": {
            "navigationBarTitleText": ""
          }
        }
      ]
    },
    {
      "root": "pages/terminal",
      "pages": [
        {
          "path": "index",
          "style": {
            "navigationBarTitleText": "设备管理"
          }
        },
        {
          "path": "terminal-bind",
          "style": {
            "navigationBarTitleText": "设备绑定"
          }
        },
        {
          "path": "terminal-change-bind",
          "style": {
            "navigationBarTitleText": "设备换绑"
          }
        },
        {
          "path": "cost-detail",
          "style": {
            "navigationBarTitleText": "费用明细"
          }
        }
      ]
    },
    {
      "root": "pages/trans",
      "pages": [
        {
          "path": "index",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "trans-detail",
          "style": {
            "navigationBarTitleText": "交易详情"
          }
        },
        {
          "path": "trans-rule",
          "style": {
            "navigationBarTitleText": "交易规则信息"
          }
        }
      ]
    },
    {
      "root": "pages/settle-info",
      "pages": [
        {
          "path": "index",
          "style": {
            "navigationBarTitleText": "结算信息"
          }
        },
        {
          "path": "update-settle-info",
          "style": {
            "navigationBarTitleText": "结算信息变更"
          }
        },
        {
          "path": "replenish-settle-info",
          "style": {
            "navigationBarTitleText": "补充结算信息"
          }
        },
        {
          "path": "setd0-settle-type",
          "style": {
            "navigationBarTitleText": "合并结算"
          }
        },
        {
          "path": "debit-card/index",
          "style": {
            "navigationBarTitleText": "收款卡管理"
          }
        },
        {
          "path": "debit-card/update-debit-card",
          "style": {
            "navigationBarTitleText": "新增收款卡"
          }
        }
      ]
    },
    {
      "root": "pages/increase-quota",
      "pages": [
        {
          "path": "index",
          "style": {
            "navigationBarTitleText": "绑卡提额"
          }
        },
        {
          "path": "credit-auth",
          "style": {
            "navigationBarTitleText": "信用认证"
          }
        }
      ]
    },
    {
      "root": "pages/user",
      "pages": [
        {
          "path": "rate-info",
          "style": {
            "navigationBarTitleText": "我的费率信息"
          }
        },
        {
          "path": "refund-manage",
          "style": {
            "navigationBarTitleText": "退汇管理"
          }
        },
        {
          "path": "update-credentials/index",
          "style": {
            "navigationBarTitleText": "法人证件信息更新",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "wish-confirm-order",
          "style": {
            "navigationBarTitleText": "意愿确认订单"
          }
        }
      ]
    },
    {
      "root": "pages/settings",
      "pages": [
        {
          "path": "index",
          "style": {
            "navigationBarTitleText": "设置"
          }
        },
        {
          "path": "about",
          "style": {
            "navigationBarTitleText": "关于"
          }
        },
        {
          "path": "logout",
          "style": {
            "navigationBarTitleText": "注销账号"
          }
        },
        {
          "path": "update-phone",
          "style": {
            "navigationBarTitleText": "更换手机号"
          }
        },
        {
          "path": "update-login-pwd",
          "style": {
            "navigationBarTitleText": "修改登录密码"
          }
        },
        {
          "path": "reset-login-pwd",
          "style": {
            "navigationBarTitleText": "登录密码找回"
          }
        }

      ]
    },
    // 机构相关页面
    {
      "root": "pages-org/settings",
      "pages": [
        {
          "path": "about",
          "style": {
            "navigationBarTitleText": "关于"
          }
        },
        {
          "path": "update-phone",
          "style": {
            "navigationBarTitleText": "更换手机号"
          }
        },
        {
          "path": "update-login-pwd",
          "style": {
            "navigationBarTitleText": "修改登录密码"
          }
        },
        {
          "path": "reset-login-pwd",
          "style": {
            "navigationBarTitleText": "机构登录密码找回"
          }
        }

      ]
    },
    {
      "root": "pages-org/notice",
      "pages": [
        {
          "path": "index",
          "style": {
            "navigationBarTitleText": "消息中心"
          }
        },
        {
          "path": "notice-detail",
          "style": {
            "navigationBarTitleText": "消息详情"
          }
        }
      ]
    },
    {
      "root": "pages-org/user",
      "pages": [
        {
          "path": "rate-policy/index",
          "style": {
            "navigationBarTitleText": "我的政策信息"
          }
        },
        {
          "path": "reward-policy/index",
          "style": {
            "navigationBarTitleText": "奖励政策信息"
          }
        },
        {
          "path": "reward-policy/reward-policy-detail",
          "style": {
            "navigationBarTitleText": "政策详情"
          }
        }
      ]
    },
    {
      "root": "pages-org/rate-policy",
      "pages": [
        {
          "path": "index",
          "style": {
            "navigationBarTitleText": "政策管理"
          }
        },
        {
          "path": "rate-policy-edit",
          "style": {
            "navigationBarTitleText": ""
          }
        },
        {
          "path": "rate-policy-sync",
          "style": {
            "navigationBarTitleText": "同步政策"
          }
        },
        {
          "path": "sub-org-list",
          "style": {
            "navigationBarTitleText": "下级机构列表"
          }
        },
        {
          "path": "activity-cashback-template-edit",
          "style": {
            "navigationBarTitleText": ""
          }
        }
      ]
    },
    {
      "root": "pages-org/drawcash",
      "pages": [
        {
          "path": "profit-manage/index",
          "style": {
            "navigationBarTitleText": "分润管理"
          }
        },
        {
          "path": "initiate/index",
          "style": {
            "navigationBarTitleText": "提现"
          }
        },
        {
          "path": "draw-record/index",
          "style": {
            "navigationBarTitleText": "提现记录"
          }
        },
        {
          "path": "draw-record/draw-record-detail",
          "style": {
            "navigationBarTitleText": "提现记录详情"
          }
        },
        {
          "path": "dlg-sign/index",
          "style": {
            "navigationBarTitleText": "出款通道签约"
          }
        }
      ]
    },
    {
      "root": "pages-org/settle-card",
      "pages": [
        {
          "path": "index",
          "style": {
            "navigationBarTitleText": "结算卡管理"
          }
        },
        {
          "path": "update-settle-card",
          "style": {
            "navigationBarTitleText": ""
          }
        }
      ]
    },
    {
      "root": "pages-org/extension-code",
      "pages": [
        {
          "path": "index",
          "style": {
            "navigationBarTitleText": "拓展码管理"
          }
        },
        {
          "path": "extension-code-update",
          "style": {
            "navigationBarTitleText": ""
          }
        },
        {
          "path": "extension-code-qrcode",
          "style": {
            "navigationBarTitleText": "团队拓展码"
          }
        }
      ]
    },
    {
      "root": "pages-org/picker-view",
      "pages": [
        {
          "path": "rate-policy/index",
          "style": {
            "navigationBarTitleText": "交易结算政策选择"
          }
        },
        {
          "path": "cashback-policy/index",
          "style": {
            "navigationBarTitleText": "活动返现政策选择"
          }
        },
        {
          "path": "target-agent",
          "style": {
            "navigationBarTitleText": "选择下级代理商"
          }
        }
      ]
    },
    {
      "root": "pages-org/income",
      "pages": [
        {
          "path": "index",
          "style": {
            "navigationBarTitleText": "收益管理"
          }
        },
        {
          "path": "profit/index",
          "style": {
            "navigationBarTitleText": "交易分润明细"
          }
        },
        {
          "path": "profit/profit-detail",
          "style": {
            "navigationBarTitleText": "分润详情"
          }
        },
        {
          "path": "cashback/index",
          "style": {
            "navigationBarTitleText": "返现明细"
          }
        },
        {
          "path": "cashback/cashback-detail",
          "style": {
            "navigationBarTitleText": "奖励详情"
          }
        }
      ]
    },
    {
      "root": "pages-org/team",
      "pages": [
        {
          "path": "agent/index",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "agent/agent-update",
          "style": {
            "navigationBarTitleText": ""
          }
        },
        {
          "path": "agent/agent-update-personal",
          "style": {
            "navigationBarTitleText": "新增团队-个人"
          }
        },
        {
          "path": "agent/agent-detail",
          "style": {
            "navigationBarTitleText": "团队详情"
          }
        },
        {
          "path": "branch/index",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "branch/branch-update",
          "style": {
            "navigationBarTitleText": "新增团队-企业"
          }
        },
        {
          "path": "branch/branch-detail",
          "style": {
            "navigationBarTitleText": "团队详情"
          }
        }
      ]
    },
    {
      "root": "pages-org/merch",
      "pages": [
        {
          "path": "index",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "merch-detail",
          "style": {
            "navigationBarTitleText": "商户详情"
          }
        },
        {
          "path": "terminal-detail",
          "style": {
            "navigationBarTitleText": "设备详情"
          }
        }
      ]
    },
    {
      "root": "pages-org/merch-report",
      "pages": [
        {
          "path": "index",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "modify-rate",
          "style": {
            "navigationBarTitleText": "费率信息"
          }
        }
      ]
    },
    {
      "root": "pages-org/terminal-manage",
      "pages": [
        {
          "path": "index",
          "style": {
            "navigationBarTitleText": "终端管理"
          }
        },
        {
          "path": "terminals/index",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "terminals/terminal-change-bind",
          "style": {
            "navigationBarTitleText": "终端换绑"
          }
        },
        {
          "path": "terminals/equity-orders",
          "style": {
            "navigationBarTitleText": "权益订单列表"
          }
        },
        {
          "path": "terminals/modify-activity",
          "style": {
            "navigationBarTitleText": "修改活动"
          }
        },
        {
          "path": "terminals/modify-rate",
          "style": {
            "navigationBarTitleText": "变更费率"
          }
        },
        {
          "path": "terminal-callback/index",
          "style": {
            "navigationBarTitleText": "机具回拨"
          }
        },
        {
          "path": "terminal-callback/terminal-callback-confirm",
          "style": {
            "navigationBarTitleText": "回拨确认"
          }
        },
        {
          "path": "transfer-record/index",
          "style": {
            "navigationBarTitleText": "调拨记录"
          }
        },
        {
          "path": "transfer-record/transfer-record-detail",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "terminal-delivery/terminal-delivery-type",
          "style": {
            "navigationBarTitleText": "类型选择"

          }
        },
        {
          "path": "terminal-delivery/index",
          "style": {
            "navigationBarTitleText": "机具下发"

          }
        },
        {
          "path": "terminal-delivery/terminal-delivery-confirm",
          "style": {
            "navigationBarTitleText": "下发确认"
          }
        },
        {
          "path": "terminal-delivery/rate-config",
          "style": {
            "navigationBarTitleText": "费率配置"
          }
        },
        {
          "path": "terminal-delivery/activity-config",
          "style": {
            "navigationBarTitleText": "活动配置"
          }
        },
        {
          "path": "rate-setting/index",
          "style": {
            "navigationBarTitleText": "费率设置"
          }
        },
        {
          "path": "rate-setting/batch-modify-rate",
          "style": {
            "navigationBarTitleText": "费率设置"
          }
        },
        {
          "path": "activity-setting/index",
          "style": {
            "navigationBarTitleText": "活动设置"
          }
        },
        {
          "path": "reward-policy/index",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "reward-policy/reward-policy-modify",
          "style": {
            "navigationBarTitleText": "修改政策"
          }
        },
        {
          "path": "reward-policy/batch-reward-policy-modify",
          "style": {
            "navigationBarTitleText": ""
          }
        },
        {
          "path": "reward-policy/select-org-list",
          "style": {
            "navigationBarTitleText": "选择机构"
          }
        }
      ]
    },
    {
      "root": "pages-org/trans",
      "pages": [
        {
          "path": "index",
          "style": {
            "navigationBarTitleText": "交易汇总"
          }
        },
        {
          "path": "trans-detail-sum",
          "style": {
            "navigationBarTitleText": "交易明细"
          }
        },
        {
          "path": "trans-detail",
          "style": {
            "navigationBarTitleText": "交易详情"
          }
        }
      ]
    }
  ],
  "preloadRule": {
    "pages/tab/home/<USER>": {
      "network": "all",
      "packages": ["pages/common"]
    }
  },
  "tabBar": {
    "color": "#7A7E83",
    "selectedColor": "#4D80F0",
    "borderStyle": "black",
    "backgroundColor": "#ffffff",
    "fontSize": "12px",
    "list": [{
      "iconPath": "static/tabbar/home.png",
      "selectedIconPath": "static/tabbar/home-hover.png",
      "pagePath": "pages/tab/home/<USER>",
      "text": "首页"
    }, {
      "iconPath": "static/tabbar/statistics.png",
      "selectedIconPath": "static/tabbar/statistics-hover.png",
      "pagePath": "pages/tab/statistic/index",
      "text": "数据统计"
    }, {
      "iconPath": "static/tabbar/user.png",
      "selectedIconPath": "static/tabbar/user-hover.png",
      "pagePath": "pages/tab/user/index",
      "text": "我的"
    }]
  },
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "万商云Pro",
    "navigationBarBackgroundColor": "#FFFFFF",
    "backgroundColor": "#FFFFFF"
  },
  "easycom": {
    "custom": {
      "^wd-(.*)": "wot-design-uni/components/wd-$1/wd-$1.vue",
      "^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)": "z-paging/components/z-paging$1/z-paging$1.vue"
    }
  }
}
