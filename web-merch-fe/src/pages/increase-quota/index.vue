<template>
  <view>
    <page-paging ref="pagingRef" v-model="datasource" :loading-more-enabled="false" @query="queryDatasource">
      <!-- 分割块 -->
      <template #top>
        <view class="gap-primary" />
      </template>

      <!-- 主体 -->
      <view class="p-20rpx">
        <view
          v-for="(item, key) in datasource" :key="key"
          class="mb-30rpx border border-#edf0f3 rounded-lg border-solid bg-white"
        >
          <!-- SN -->
          <view class="flex items-center rounded-t-lg bg-#f4f4f4 px-20rpx py-16rpx font-bold">
            <text class="text-15px">
              SN号: {{ item.termSn }}
            </text>
          </view>

          <view class="p-8px">
            <wd-cell-group custom-class="cell-group">
              <wd-cell title="机构名称" :value="item.channelName" />
              <wd-cell title="通道商户编号">
                <view class="flex items-center justify-end">
                  <text>{{ item.chlMerchantNo }}</text>
                </view>
              </wd-cell>
            </wd-cell-group>
          </view>
        </view>
      </view>

      <!-- 底部 -->
      <template #bottom>
        <view class="p-40rpx">
          <wd-button block size="large" type="primary" @click="toAuth">
            信用认证
          </wd-button>
        </view>
      </template>
    </page-paging>
    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import { TerminalApi } from '@/api/terminal';

const pagingRef = ref();

const datasource = ref<Record<string, any>>([]);

onShow(() => {
  pagingRef.value?.reload();
});

function queryDatasource() {
  pagingRef.value.complete([]);

  // TerminalApi.queryChlMerchTerminalList({ })
  //   .then((data) => {
  //     pagingRef.value.complete(data);
  //   })
  //   .catch(() => {
  //     pagingRef.value.complete(false);
  //   });
}

function toAuth() {
  uni.navigateTo({ url: `/pages/increase-quota/credit-auth` });
}
</script>

<style lang="scss" scoped>
:deep(.cell-group) {
  .wd-cell {
    @apply p-0;
  }

  .wd-cell__wrapper {
    @apply p-2px;
  }
}
</style>
