<template>
  <view v-if="show" class="h-full flex flex-col overflow-hidden bg-primary">
    <!-- <wd-cell
      title="商户类型" title-width="100px" center
      custom-class="my-10px shrink-0"
    >
      <wd-radio-group
        v-model="merchType"
        inline :disabled="handleType === '3'"
        custom-class="flex items-center justify-end"
      >
        <wd-radio :value="0">
          小微商户
        </wd-radio>
        <wd-radio :value="1">
          企业商户
        </wd-radio>
      </wd-radio-group>
    </wd-cell> -->

    <!-- 切换组件 -->
    <view class="grow overflow-y-hidden">
      <scroll-view
        scroll-y
        :show-scrollbar="false"
        :scroll-top="scrollTop"
        :throttle="false"
        class="h-full"
      >
        <DredgeMicro v-if="merchType === 0" :merch-info="merchInfo" :handle-type="handleType" />
        <DredgeCompany v-else-if="merchType === 1" :merch-info="merchInfo" :handle-type="handleType" />
      </scroll-view>
    </view>
  </view>
  <wd-toast />
</template>

<script setup lang="ts">
import { useToast } from 'wot-design-uni';
import DredgeMicro from './modules/dredge-micro.vue';
import DredgeCompany from './modules/dredge-company.vue';
import { MerchApi } from '@/api/merch';

/**
 * 商户类型
 * 0: 小微商户 1: 企业商户
 */
const merchType = ref(0);

const merchInfo = ref<any>({});

const show = ref(false);

const toast = useToast();

const handleType = ref('2');
const handleTypeMap: any = {
  1: '基本信息提交',
  2: '商户开通',
  3: '驳回重提',
  4: '基本信息提交',
};

const scrollTop = ref<number>(0);

onBackPress((options) => {
  if (options.from === 'backbutton') {
    onBeforeBackPress();
    return true;
  }
});

const isControlAppletBack = ref(true);
onUnload(() => {
  // #ifdef MP-WEIXIN
  const pages = getCurrentPages();
  const lastPage = pages[pages.length - 2];
  if (lastPage?.route !== 'pages/report/merch-report/index') {
    if (isControlAppletBack.value)
      onBeforeBackPress();
  }
  // #endif
});

// 返回处理
function onBeforeBackPress() {
  uni.redirectTo({ url: '/pages/report/merch-report/index' });
}

onLoad((options: any) => {
  handleType.value = options.handleType || handleType.value;
  merchInfo.value.id = options.id || '';
  merchInfo.value.terminalSn = options.terminalSn || '';
  if (handleType.value === '3') {
    merchType.value = +options.isMicro === 1 ? 0 : 1;
  }
  isControlAppletBack.value = true;
  getMerchInfo();
});

/**
 * 获取商户信息
 */
async function getMerchInfo() {
  toast.loading('加载中...');

  const res = await MerchApi.queryMerchInfo().catch(() => {
    toast.close();
    // return Promise.reject();
  });
  merchInfo.value = Object.assign(merchInfo.value, res);

  toast.close();

  if (merchInfo.value.merchant?.haveElecSignatureStatus) {
    isControlAppletBack.value = false;
    uni.navigateTo({ url: '/pages/report/merch-signature/protocol/index' });
    return;
  }

  if (handleType.value !== '4' && !merchInfo.value.merchant?.directAgentNo) {
    isControlAppletBack.value = false;
    uni.navigateTo({ url: '/pages/report/merch-dredge/terminal-entry' });
    return;
  }

  uni.setNavigationBarTitle({
    title: handleTypeMap[handleType.value],
  });
  show.value = true;
}
</script>
