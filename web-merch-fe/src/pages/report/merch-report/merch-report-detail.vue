<template>
  <view class="h-full overflow-y-scroll bg-primary">
    <wd-skeleton
      theme="image" animation="gradient" :loading="loading" :row-col="skeletonConf"
    >
      <wd-cell-group title="商户信息" custom-class="cell-group">
        <wd-cell title="商户名称" :value="detail.chlMerchantName" />
        <wd-cell title="商户编号" :value="detail.chlMerchantNo" />
        <wd-cell title="商户类型" :value="detail.isMicro === 1 ? '小微' : '企业'" />
        <wd-cell title="创建时间" :value="detail.createTime" />
        <wd-cell title="入网状态">
          {{ reportRespCodesMap[detail.reportRespCode] || '--' }}
        </wd-cell>
      </wd-cell-group>

      <wd-cell-group title="结算信息" custom-class="cell-group">
        <wd-cell title="结算人姓名" :value="detail.bankAccountName" />
        <wd-cell title="结算卡卡号" :value="detail.bankAccountNoMask" />
        <wd-cell title="结算银行名称" :value="detail.bankName" />
        <wd-cell title="结算支行名称" :value="detail.bankBranch" />
        <wd-cell title="通道审核状态">
          {{ changeChannelStatusMap[detail.changeChannelStatus] || '--' }}
        </wd-cell>
        <wd-cell v-if="detail.changeChannelStatus === 2" title="变更失败原因" :value="detail.channelRespMsg || '--'" />
      </wd-cell-group>

      <wd-cell-group title="交易开通状态" custom-class="cell-group">
        <wd-cell title="银联交易">
          <view class="flex items-center justify-end">
            <wd-tag type="primary" mark>
              {{ formatStatusDesc('union', detail.unionpayOpenStatus) }}
            </wd-tag>
            <i
              v-if="detail.unionpayOpenStatus === 3" class="i-mdi-chat-question-outline ml-4px"
              @click="showErrorMsg('union', detail.unionMessage)"
            />
          </view>
        </wd-cell>
        <wd-cell title="支付宝扫码">
          <view class="flex items-center justify-end">
            <wd-tag type="primary" mark>
              {{ formatStatusDesc('alipay', detail.alipayOpenStatus) }}
            </wd-tag>
            <i
              v-if="detail.alipayOpenStatus === 3" class="i-mdi-chat-question-outline ml-4px"
              @click="showErrorMsg('alipay', detail.alipayMessage)"
            />
          </view>
        </wd-cell>
        <wd-cell title="微信扫码">
          <view class="flex items-center justify-end">
            <wd-tag type="primary" mark>
              {{ formatStatusDesc('wechat', detail.wechatOpenStatus) }}
            </wd-tag>
            <i
              v-if="detail.wechatOpenStatus === 3" class="i-mdi-chat-question-outline ml-4px"
              @click="showErrorMsg('wechat', detail.wechatMessage)"
            />
          </view>
        </wd-cell>
        <wd-cell title="EPOS支付">
          <view class="flex items-center justify-end">
            <wd-tag type="primary" mark>
              {{ formatStatusDesc('epos', detail.eposOpenStatus) }}
            </wd-tag>
            <i
              v-if="detail.eposOpenStatus === 3" class="i-mdi-chat-question-outline ml-4px"
              @click="showErrorMsg('epos', detail.eposMessage)"
            />
          </view>
        </wd-cell>
      </wd-cell-group>

      <!-- 底部操作按钮 -->
      <!-- <view class="mx-40rpx my-60rpx">
        <wd-button type="primary" size="large" block @click="toModifyMercnName">
          修改商户名称
        </wd-button>
        <view class="mt-20rpx text-center text-#b51e1e">
          365天内只允许修改三次
        </view>
      </view> -->
    </wd-skeleton>

    <wd-message-box />
  </view>
</template>

<script setup lang="ts">
import { useMessage } from 'wot-design-uni';
import { MerchReportApi } from '@/api/report';
import { CHANNEL_CODE } from '@/config/setting';

const message = useMessage();

const detail = ref<any>({});

const where = reactive({
  id: '',
});

const loading = ref<boolean>(true);

const reportRespCodesMap: Record<string, any> = {
  1: '审核中',
  2: '入网成功',
  3: '重新提交',
  99: '已关闭',
  10: '电子协议待签署',
  15: '待用户选择结算卡',
};

const changeChannelStatusMap: Record<number, string> = {
  0: '初始状态',
  1: '变更成功',
  2: '变更失败',
  3: '审核中',
  6: '审核中,待处理',
};

const channelOpenStatusMap: Record<number, string> = {
  '-1': '暂不支持',
  '0': '未开通',
  '1': '已开通',
  '3': '开通失败',
};
const unionpayOpenStatusMap: Record<number, string> = {
  '-1': '暂不支持',
  '0': '未开通',
  '1': '已开通',
  '2': '开通中',
  '3': '开通失败',
  '4': '银联-指定结算卡',
  '11': '开通中',
};
type ChannelType = 'union' | 'wechat' | 'alipay' | 'epos';
function formatStatusDesc(type: ChannelType, status: number) {
  if (status === null) {
    return '--';
  }
  switch (type) {
    case 'union':
      return `${unionpayOpenStatusMap[status]}`;
    case 'wechat':
      return `${channelOpenStatusMap[status]}`;
    case 'alipay':
      return `${channelOpenStatusMap[status]}`;
    case 'epos':
      return `${channelOpenStatusMap[status]}`;
    default:
      return '--';
  }
}

const skeletonConf = [
  { width: '100%', height: '200px', marginBottom: '20px' },
  { width: '100%', height: '200px', marginBottom: '20px' },
  { width: '100%', height: '200px', marginBottom: '20px' },
];

onLoad((query) => {
  where.id = query?.id;
  supportChangeSettleCard();
});

onShow(() => {
  getDetail();
});

async function getDetail() {
  const data = await MerchReportApi.detail({ ...where });
  detail.value = { ...data?.merchantBankCard, ...data?.merchantSignOrder };
  loading.value = false;
}

async function supportChangeSettleCard() {
  const data = await MerchReportApi.supportChangeSettleCard({
    channelCode: CHANNEL_CODE,
  });
  console.log('supportChangeSettleCard', data);
}

function toModifyMercnName() {
  uni.navigateTo({ url: `/pages/report/merch-report/modify-merch-name?chnMerchNo=${detail.value.chlMerchantNo}` });
}

/** 显示报备失败原因 */
function showErrorMsg(type: ChannelType, msg: string) {
  let title = '';
  switch (type) {
    case 'union':
      title = '银联交易';
      break;
    case 'wechat':
      title = '微信扫码';
      break;
    case 'alipay':
      title = '支付宝扫码';
      break;
    case 'epos':
      title = 'EPOS支付';
      break;
  }

  title += '开通失败原因';

  message.alert({
    msg,
    title,
  });
}
</script>

<style lang="scss" scoped>
:deep(.cell-group){
  @apply mt-20rpx;

   .wd-cell__wrapper{
    @apply pt-0 ;
  }
}
</style>
