<template>
  <view class="h-full overflow-y-scroll bg-primary">
    <view class="gap-primary" />
    <wd-form ref="formRef" :model="form" :rules="rules">
      <!-- 结算信息 -->
      <wd-cell-group border>
        <wd-cell title="结算人类型" title-width="100px" prop="isLegalSettle" center>
          <wd-radio-group
            v-model="form.isLegalSettle"
            shape="dot"
            custom-class="flex items-center justify-end"
            inline
          >
            <wd-radio :value="1">
              法人结算
            </wd-radio>
            <!-- <wd-radio :value="0">
              非法人结算
            </wd-radio> -->
          </wd-radio-group>
        </wd-cell>

        <wd-input
          v-model="form.shortName" prop="shortName"
          label="商户简称" placeholder="商户简称" label-width="100px"
          clearable align-right readonly
        />
        <wd-cell
          title="行业类别" title-width="100px" prop="unionMcc" is-link
          @click="onSelectUnionMcc"
        >
          <wd-textarea
            v-model="form.unionMccName"
            placeholder="请选择行业MCC"
            auto-height no-border readonly
            custom-textarea-class="text-right"
          />
        </wd-cell>
      </wd-cell-group>

      <view class="mt-10px bg-white">
        <view class="p-12px text-26rpx font-medium">
          资质图片上传
        </view>
        <view class="flex flex-wrap">
          <view
            v-for="(item, key) in
              [
                fileMap[11], fileMap[12], fileMap[13],
              ]"
            :key="key"
            class="mb-12px basis-1/2"
          >
            <GivenUpload
              v-model:file-data="item.fileData"
              :file-name="item.fileName"
              :placeholder="item.placeholder"
              custom-class="w-full"
            />
          </view>
        </view>
      </view>

      <!-- 企业信息 -->
      <wd-cell-group title="企业信息" border custom-class="mt-10px">
        <view class="flex flex-wrap justify-center bg-primary py-15px">
          <view v-for="(item, key) in [fileMap[7]]" :key="key" class="basis-1/2">
            <GivenUpload
              v-model:file-data="item.fileData"
              :file-name="item.fileName"
              :placeholder="item.placeholder"
              custom-class="w-full"
              @choose="onChooseFile"
            />
          </view>
        </view>
        <wd-input
          v-model="form.licenseName" prop="licenseName"
          label="企业名称" placeholder="可图片自动识别" label-width="100px"
          clearable align-right
        />
        <wd-textarea
          v-model="form.licenseNo" prop="licenseNo"
          label="统一社会信用代码" placeholder="可图片自动识别"
          clearable auto-height
          custom-textarea-class="text-right"
        />
        <wd-textarea
          v-model="form.licenseAddr" prop="licenseAddr"
          label="经营详细地址" placeholder="可图片自动识别" label-width="100px"
          clearable auto-height
          custom-textarea-class="text-right"
        />
        <wd-datetime-picker
          v-model="licenseDateRegion" prop="licenseEndDate"
          label="营业证件有效期"
          type="date" align-right
          :min-date="minDateLicense" :max-date="maxDateLicense"
          :default-value="defaultDateRegionValue"
          @confirm="onConfirmLicenseDateRegion"
        />
        <wd-input
          v-if="form.isLegalSettle === 0"
          v-model="form.legalCertNo" prop="legalCertNo"
          :rules="[{ required: true, pattern: idCardFormat, message: '请正确输入身份证号' }]"
          label="法人身份证号" placeholder="请输入法人身份证号" label-width="100px"
          clearable align-right
        />
      </wd-cell-group>

      <view class="p-40rpx">
        <wd-button type="primary" size="large" block @click="save">
          提交
        </wd-button>
      </view>
    </wd-form>

    <!-- 挂载点 -->
    <wd-toast />
    <wd-message-box :close-on-click-modal="false" />
  </view>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'wot-design-uni/components/wd-form/types';
import { useMessage, useToast } from 'wot-design-uni';
import dayjs from 'dayjs';
import type { GivenUploadProps } from '@/components/given-upload/type';
import { CommonApi } from '@/api/common/index';
import { CHANNEL_CODE } from '@/config/setting';
import { emitter } from '@/utils/emitter';
import { MerchReportApi } from '@/api/report';
import { decodeUrlParams } from '@/utils';

const toast = useToast();
const message = useMessage();

const licenseDateRegion = ref<any>([]);
const defaultDateRegionValue = ref([dayjs().valueOf(), dayjs().valueOf()]);

// 营业执照有效期选择范围
const minDateLicense = dayjs().subtract(50, 'year').valueOf();
const maxDateLicense = dayjs('2099-12-31').valueOf();

// 表单
const form = reactive<any>({
  isLegalSettle: 1,
  channelCode: CHANNEL_CODE, // 通道编号 必填
  chnMerchNo: '', // 通道商户编号 必填
  shortName: '', // 商户简称必填
  unionMcc: '', // 银联MCC必填
  unionMccName: '',

  licenseName: '', // 企业名称
  licenseNo: '', // 营业执照号
  licenseAddr: '', // 企业注册地址
  licenseStartDate: '', // 营业执照有效期（开始日期）
  licenseEndDate: '', // 营业执照有效期（结束日期）
  // legalName: '', // 法人姓名
  legalCertNo: '', // 法人证件号 企业-非法人结算（必填）

  imageJsonList: [],
});

const idCardFormat: RegExp = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9X]$/i;
const rules: FormRules = {
  shortName: [{ required: true, message: '商户简称不能为空' }],
  unionMcc: [{ required: true, message: '请选择行业类别' }],
  isLegalSettle: [{ required: true, message: '请选择结算人类型' }],

  licenseName: [{ required: true, message: '请输入企业名称' }],
  licenseNo: [{ required: true, message: '请输入统一社会信用代码' }],
  licenseAddr: [{ required: true, message: '请输入经营详细地址' }],
  licenseEndDate: [{ required: true, message: '请选择营业证件有效期' }],
  // legalName: [{ required: true, message: '请输入法人姓名' }],
};

const formRef = ref<FormInstance | null>(null);

// 文件
type FileType = 7 | 11 | 12 | 13 ;
const fileMap = ref <Record<FileType, GivenUploadProps>> ({
  7: {
    fileName: '营业执照照片',
    placeholder: require('@/static/images/bank_card.png'),
    fileData: '',
    fileType: 7,
  },
  11: {
    fileName: '门店门头照',
    placeholder: require('@/static/images/door_photo.png'),
    fileData: '',
    fileType: 11,
  },
  12: {
    fileName: '店内环境照片',
    placeholder: require('@/static/images/store_environment_photo.png'),
    fileData: '',
    fileType: 12,
  },
  13: {
    fileName: '收银台照',
    placeholder: require('@/static/images/cashier_photo.png'),
    fileData: '',
    fileType: 13,
  },
});

onLoad((query: any) => {
  query = decodeUrlParams(query);
  form.chnMerchNo = query?.chnMerchNo;
  form.shortName = query?.shortName;

  if (query?.resubmit === '1') {
    queryResubmitInfo();
  }
});

async function queryResubmitInfo() {
  const res = await MerchReportApi.merchAudit({
    channelCode: CHANNEL_CODE, // 通道编号  必填
    chlMerchantNo: form.chnMerchNo, // 通道商户编号  必填
    modifyType: 2, // 信息修改类型  必填 1-修改商户简称 2-小微转企业
  });

  form.id = res.id;

  const merchReportReqDTO = res.merchReportReqDTO || {};
  Object.keys(form).forEach((key) => {
    form[key] = merchReportReqDTO[key] || form[key];
  });

  if (merchReportReqDTO.licenseStartDate && merchReportReqDTO.licenseEndDate) {
    licenseDateRegion.value = [dayjs(merchReportReqDTO.licenseStartDate).valueOf(), dayjs(merchReportReqDTO.licenseEndDate).valueOf()];
  }

  const imageList = res?.merchReportReqDTO?.imageJsonList || [];
  if (imageList.length) {
    const fileList = Object.values(fileMap.value);
    fileList.forEach((item) => {
      imageList.forEach((item2) => {
        if (item2?.imageType === item.fileType) {
          item.fileData = item2?.imagePath;
          item.id = item2?.id;
        }
      });
    });
  }
}

async function save() {
  // 检验表单
  const { valid, errors } = await formRef.value!.validate();
  if (!valid)
    return Promise.reject(errors);

  // 校验文件
  await checkFile();
  // 上传文件
  const imageJsonList = await uploadFile();
  form.imageJsonList = imageJsonList;

  const data = await MerchReportApi.microToEnterReport(form);

  // 进件成功
  if (data?.code) {
    toast.success({
      msg: data?.message || '操作成功',
      closed: () => {
        uni.navigateBack();
      },
    });
  }
  // 进件失败
  else {
    message.alert({
      title: '提示',
      msg: data?.message,
    }).then(() => {
      uni.navigateBack();
    });
  }
}

/*
 * 校验文件
 */
async function checkFile() {
  const fileList = Object.values(fileMap.value);
  const isCheckPass = fileList.every(f => !!f.fileData || f.show === false);
  if (!isCheckPass) {
    toast.warning('图片上传不完整');
    return Promise.reject(new Error('upload check fail !'));
  }
  return Promise.resolve();
}

/**
 * 上传文件
 */
async function uploadFile() {
  const fileList = Object.values(fileMap.value);
  const fileListHasVal = fileList.filter(i => !!i.fileData);
  const noChangeFiles: any = [];
  const changeedFiles: any = [];
  fileListHasVal.forEach((i: GivenUploadProps) => {
    if (/^(https?:)/.test(i.fileData as string)) {
      noChangeFiles.push({
        id: i.id,
        imageType: i.fileType,
        imagePath: i.fileData,
      });
    }
    else {
      changeedFiles.push({
        fileType: i.fileType,
        suffixType: 'png',
        fileData: i.fileData,
      });
    }
  });

  let imageJsonList: any = [];
  if (changeedFiles.length) {
    const data = await CommonApi.uploadImages({ channelCode: CHANNEL_CODE, imageList: changeedFiles })
      .catch(() => Promise.reject(new Error('upload fail!')));
    imageJsonList = data?.imageJsonList;
  }

  return [...noChangeFiles, ...imageJsonList];
}

function onConfirmLicenseDateRegion() {
  const [startDate, endDate] = licenseDateRegion.value;
  form.licenseStartDate = dayjs(startDate).format('YYYY-MM-DD');
  form.licenseEndDate = dayjs(endDate).format('YYYY-MM-DD');
}

/** 选择营业执照&ocr */
async function onChooseFile(value: string) {
  const res = await CommonApi.ocrBusinessLicense({ imgFile: value });
  if (res?.success) {
    Object.keys(res).forEach((key) => {
      res[key] = res[key] === 'FailInRecognition' ? '' : res[key];
    });

    const { regNum, name, person, address, establishDate, validPeriod } = res;
    form.licenseName = name;
    form.licenseNo = regNum;
    form.licenseAddr = address;
    // form.legalName = person;

    if (establishDate && validPeriod) {
      let endDate = validPeriod;
      if (validPeriod === '长期') {
        endDate = '2099-12-31';
      }
      licenseDateRegion.value = [dayjs(establishDate).valueOf(), dayjs(endDate).valueOf()];
      onConfirmLicenseDateRegion();
    }
  }
}

function onSelectUnionMcc() {
  uni.navigateTo({ url: '/pages/picker-view/category/channel-category' });
  emitter.on('picker-category', (item) => {
    form.unionMcc = item.mcc;
    form.unionMccName = item.mccName;
  });
}
</script>

<style lang="scss" scoped>
:deep(){
  .wd-input__error-message,
  .wd-cell__error-message,
  .wd-textarea__error-message,
  .wd-picker__error-message
  {
    text-align: right !important;
  }
}
</style>
