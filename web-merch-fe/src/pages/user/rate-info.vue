<template>
  <view>
    <page-paging ref="pagingRef" v-model="dataSource" @query="queryDataSource">
      <template #top>
        <view class="gap-primary" />
      </template>

      <!-- 主体 -->
      <view class="p-20rpx">
        <view
          v-for="(item, key) in dataSource" :key="key"
          class="mb-20rpx border border-#e8e8e8 rounded-lg border-solid bg-white"
        >
          <view class="rounded-t-lg bg-#f4f4f4 px-20rpx py-16rpx">
            <text class="font-medium">
              {{ item.chnMerchantNo }}({{ item.chnMerchantName }})
            </text>
          </view>
          <view class="p-20rpx text-26rpx">
            <wd-row :gutter="10">
              <wd-col :span="12">
                <view class="custom-cell">
                  <text>银联标准(贷)</text>
                  <view> {{ item.rateInfoDTO.creditRate }} </view>
                </view>
                <view class="custom-cell">
                  <text>银联标准(借)</text>
                  <view> {{ item.rateInfoDTO.debitRate }} </view>
                </view>
                <view class="custom-cell">
                  <text>银联云闪付(贷)</text>
                  <view> {{ item.rateInfoDTO.nfcCreditRate }} </view>
                </view>
                <view class="custom-cell">
                  <text>微信</text>
                  <view> {{ item.rateInfoDTO.wechatRate }} </view>
                </view>
                <!-- <view class="custom-cell">
                  <text>D0费率(贷)</text>
                  <view> {{ item.rateInfoDTO.creditQrD0Rate }} </view>
                </view>
                <view class="custom-cell">
                  <text>D0费率(借)</text>
                  <view> {{ item.rateInfoDTO.debitQrD0Rate }} </view>
                </view> -->
              </wd-col>
              <wd-col :span="12">
                <!-- 占位以换行 -->
                <view class="custom-cell invisible">
                  <view> empty </view>
                </view>
                <view class="custom-cell">
                  <text>银联标准封顶(借)</text>
                  <view> {{ item.rateInfoDTO.debitFeeMax }} </view>
                </view>
                <view class="custom-cell">
                  <text>银联云闪付(借)</text>
                  <view> {{ item.rateInfoDTO.nfcDebitRate }} </view>
                </view>
                <view class="custom-cell">
                  <text>支付宝</text>
                  <view> {{ item.rateInfoDTO.aliPayRate }} </view>
                </view>
                <!-- <view class="custom-cell">
                  <text>D0单笔(贷)</text>
                  <view> {{ item.rateInfoDTO.creditQrD0SingleFee }} </view>
                </view>
                <view class="custom-cell">
                  <text>D0单笔(借)</text>
                  <view> {{ item.rateInfoDTO.debitQrD0SingleFee }} </view>
                </view> -->
              </wd-col>
            </wd-row>
          </view>
        </view>
      </view>
    </page-paging>
    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import { MerchReportApi } from '@/api/report';
import { CHANNEL_CODE } from '@/config/setting';

const pagingRef = ref();

const where = reactive({
  channelCode: CHANNEL_CODE,
});

const dataSource = ref<any[]>([]);

function queryDataSource(pageNo: number, pageSize: number) {
  MerchReportApi.queryChlMerchRateInfoPage({ ...where, pageNo, pageSize })
    .then((res) => {
      res?.rows.forEach((item: any) => {
        item.rateInfoDTO = item.rateInfoDTO || {};
      });

      pagingRef.value.completeByTotal(res?.rows, res?.totalRows);
    })
    .catch(() => {
      pagingRef.value.completeByTotal(false);
    });
}
</script>

<style lang="scss" scoped>
.custom-cell{
    @apply flex items-center justify-between mb-20rpx;

    view{
      @apply w-110rpx break-all rounded-md bg-primary p-10rpx text-center text-30rpx;
    }
  }
</style>
