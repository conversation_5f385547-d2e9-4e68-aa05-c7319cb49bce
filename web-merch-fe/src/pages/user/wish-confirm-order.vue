<template>
  <view class="h-full">
    <page-paging ref="pagingRef" v-model="datasource" :loading-more-enabled="false" @query="queryDataSource">
      <!-- 分割块 -->
      <template #top>
        <view class="gap-primary" />
      </template>

      <!-- 主体 -->
      <view class="p-20rpx">
        <view
          v-for="(item, key) in datasource" :key="key"
          class="mb-30rpx border border-#e8e8e8 rounded-lg border-solid bg-white"
        >
          <view class="p-16rpx">
            <wd-cell-group custom-class="cell-group">
              <wd-cell title="订单号" :value="item.orderNo" />
              <wd-cell title="订单类型" :value="orderTypeMap[item.orderType]" />
              <wd-cell title="订单金额" :value="item.originPayAmt" />
              <wd-cell title="终端SN号" :value="item.terminalSn" />
              <wd-cell title="通道商户户" :value="item.chlMerchantNo" />
              <wd-cell title="是否需要意愿核身" :value="item.isNeedIdentityVert === 1 ? '需要' : '不需要'" />
              <wd-cell title="意愿核身状态">
                <wd-text type="primary" :text="checkSelfStatusMap[item.checkSelfStatus]" />
              </wd-cell>
            </wd-cell-group>
          </view>

          <view
            v-if="item.isNeedIdentityVert === 1 && item.checkSelfStatus === 0"
            class="flex justify-center border border-#e8e8e8 border-t-solid"
          >
            <text
              class="p-y20rpx text-#4d80f0"
              @click="handleInitiate(item)"
            >
              发起意愿确认
            </text>
          </view>
        </view>
      </view>
    </page-paging>
    <wd-message-box />
  </view>
</template>

<script lang="ts" setup>
import { TransApi } from '@/api/trans';

const pagingRef = ref();

const orderTypeMap: EnumMap = {
  1: '押金服务费',
  2: 'Sim流量服务费',
};

const checkSelfStatusMap: EnumMap = {
  0: '待完成',
  1: '已完成',
};

const datasource = ref<any[]>([]);

function queryDataSource() {
  TransApi.queryNeedInentityVertOrderList()
    .then((data) => {
      pagingRef.value.complete(data);
    })
    .catch(() => {
      pagingRef.value.complete(false);
    });
}

async function handleInitiate(item: any) {
  const data = await TransApi.merchantCheckMerSelf({
    payOrderNo: item.orderNo,
    clientType: 2,
  });

  if (data?.url) {
    location.href = data.url;
  }
}
</script>

<style lang="scss" scoped>
:deep(.cell-group) {
  .wd-cell {
    @apply p-0;
  }

  .wd-cell__wrapper {
    @apply p-2px;
  }
}
</style>
