import { defineStore } from 'pinia';
import type { DataStats, TransferItem, TransferOptions, TransferState } from './types';

/**
 * Transfer Store
 * 用于页面间数据传递
 */
export const useTransferStore = defineStore('transfer', {
  state: (): TransferState => ({
    transferData: {} as Record<string, TransferItem>,
  }),

  getters: {
    /**
     * 获取数据统计信息
     */
    getStats(): DataStats {
      const now = Date.now();
      let totalSize = 0;
      let expiredCount = 0;
      let validCount = 0;

      Object.values(this.transferData).forEach((item) => {
        if (item.expire && item.expire <= now) {
          expiredCount++;
        }
        else {
          validCount++;
          totalSize += JSON.stringify(item.data).length;
        }
      });

      return {
        totalItems: Object.keys(this.transferData).length,
        validItems: validCount,
        expiredItems: expiredCount,
        totalSize,
      };
    },
  },

  actions: {
    /**
     * 存储数据
     * @param key 存储键
     * @param data 要存储的数据
     * @param options 存储选项
     * @returns 实际使用的key
     */
    setData(key: string, data: any, options: TransferOptions = {}): string {
      const now = Date.now();
      const expire = options.expire ? now + options.expire : undefined;

      const item: TransferItem = {
        data,
        timestamp: now,
        expire,
        autoRemove: options.autoRemove ?? true,
      };

      this.transferData[key] = item;

      return key;
    },

    /**
     * 获取数据
     * @param key 存储键
     * @param autoRemove 是否自动删除
     * @returns 存储的数据
     */
    getData(key: string, autoRemove?: boolean): any {
      try {
        const item = this.transferData[key];

        if (!item) {
          return null;
        }

        const now = Date.now();

        // 检查是否过期
        if (item.expire && now > item.expire) {
          delete this.transferData[key];
          return null;
        }

        // 自动删除
        const shouldRemove = autoRemove !== undefined ? autoRemove : item.autoRemove;
        if (shouldRemove) {
          delete this.transferData[key];
        }

        return item.data;
      }
      catch (error) {
        console.error('[TransferStore] 获取数据失败:', error);
        return null;
      }
    },

    /**
     * 检查数据是否存在且有效
     * @param key 存储键
     * @returns 是否存在
     */
    hasData(key: string): boolean {
      const item = this.transferData[key];
      if (!item) {
        return false;
      }

      // 检查是否过期
      if (item.expire && Date.now() > item.expire) {
        delete this.transferData[key];
        return false;
      }

      return true;
    },

    /**
     * 删除指定数据
     * @param key 存储键
     * @returns 是否删除成功
     */
    removeData(key: string): boolean {
      if (key in this.transferData) {
        delete this.transferData[key];
        return true;
      }
      return false;
    },

    /**
     * 清理过期数据
     * @returns 清理的数据数量
     */
    cleanup(): number {
      let cleanedCount = 0;
      const now = Date.now();

      Object.keys(this.transferData).forEach((key) => {
        const item = this.transferData[key];
        if (item.expire && now > item.expire) {
          delete this.transferData[key];
          cleanedCount++;
        }
      });

      return cleanedCount;
    },

    /**
     * 清空所有数据
     */
    clearAll(): void {
      this.transferData = {};
    },

    /**
     * 生成唯一key
     * @param prefix 前缀
     * @returns 唯一key
     */
    generateKey(prefix = 'transfer'): string {
      return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    },
  },

  // 持久化配置
  persist: {
    enabled: true,
    H5Storage: window?.localStorage,
  },
});

export default useTransferStore;
